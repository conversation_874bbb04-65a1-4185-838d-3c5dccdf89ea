{"name": "WG02", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "<PERSON>", "files": [{"path": "../WG02-V194-2/Hal/hal_led.c"}, {"path": "../WG02-V194-2/Hal/hal_timer.c"}, {"path": "../WG02-V194-2/Hal/hal_gpio.c"}, {"path": "../WG02-V194-2/Hal/hal_flash.c"}, {"path": "../WG02-V194-2/Hal/hal_tftlcd.c"}, {"path": "../WG02-V194-2/Hal/hal_task.c"}, {"path": "../WG02-V194-2/Hal/hal_wtn6.c"}, {"path": "../WG02-V194-2/Hal/hal_al6630.c"}, {"path": "../WG02-V194-2/Hal/hal_key.c"}, {"path": "../WG02-V194-2/Hal/hal_uart.c"}, {"path": "../WG02-V194-2/Hal/hal_adc.c"}, {"path": "../WG02-V194-2/Hal/hal_eeprom.c"}], "folders": []}, {"name": "User", "files": [{"path": "../WG02-V194-2/User/main.c"}, {"path": "../WG02-V194-2/User/stm32f10x_it.c"}, {"path": "../WG02-V194-2/User/system_stm32f10x.c"}], "folders": []}, {"name": "Mt", "files": [{"path": "../WG02-V194-2/mt/mt_flash.c"}, {"path": "../WG02-V194-2/mt/mt_tftlcd.c"}, {"path": "../WG02-V194-2/mt/lcdfont.c"}, {"path": "../WG02-V194-2/mt/mt_task.c"}, {"path": "../WG02-V194-2/mt/mt_lora.c"}, {"path": "../WG02-V194-2/mt/mt_api.c"}, {"path": "../WG02-V194-2/mt/mt_wifi.c"}, {"path": "../WG02-V194-2/mt/mt_4g.c"}, {"path": "../WG02-V194-2/mt/mt_mqtt.c"}, {"path": "../WG02-V194-2/mt/MT_MD5.lib"}, {"path": "../WG02-V194-2/mt/mt_protocol.c"}, {"path": "../WG02-V194-2/mt/mt_driver.c"}, {"path": "../WG02-V194-2/mt/mt_update.c"}], "folders": []}, {"name": "App", "files": [{"path": "../WG02-V194-2/App/app.c"}, {"path": "../WG02-V194-2/App/para.c"}], "folders": []}, {"name": "OS", "files": [{"path": "../WG02-V194-2/OS/OS_System.c"}, {"path": "../WG02-V194-2/OS/cpu.c"}], "folders": []}, {"name": "Startup", "files": [{"path": "../WG02-V194-2/Startup/startup_stm32f10x_cl.s"}], "folders": []}, {"name": "Libraries", "files": [{"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/misc.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_adc.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_bkp.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_can.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_cec.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_crc.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_dac.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_dbgmcu.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_dma.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_exti.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_flash.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_fsmc.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_gpio.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_i2c.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_iwdg.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_pwr.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_rcc.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_rtc.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_sdio.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_spi.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_tim.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_usart.c"}, {"path": "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/src/stm32f10x_wwdg.c"}, {"path": "../WG02-V194-2/Libraries/CMSIS/CM3/CoreSupport/core_cm3.c"}], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": ".pack/Keil/STM32F1xx_DFP.2.4.1", "miscInfo": {"uid": "a9b75f280aa0563d170da86fe518d067"}, "targets": {"Target 1": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M3", "archExtensions": "", "floatingPointHardware": "none", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x10000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x40000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "OpenOCD", "uploadConfig": {"bin": "", "target": "stm32f1x", "interface": "stlink", "baseAddr": "0x08000000"}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": [".", "../WG02-V194-2/mdk", "../WG02-V194-2/App", "../WG02-V194-2/Hal", "../WG02-V194-2/Libraries/CMSIS/CM3/CoreSupport", "../WG02-V194-2/Libraries/STM32F10x_StdPeriph_Driver/inc", "../WG02-V194-2/mt", "../WG02-V194-2/OS", "../WG02-V194-2/User", ".cmsis/include", "../WG02-V194-2/mdk/RTE/_Target 1", "../WG02-V194-2/Libraries/CMSIS/CM3/DeviceSupport/ST/STM32F10x"], "libList": [], "defineList": ["USE_STDPERIPH_DRIVER", "STM32F10X_CL"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "cd .\\..\\WG02-V194-2\\mdk && mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${ProjectName}.axf\"", "disable": false, "abortAfterFailed": true}, {"name": "fromelf.exe --bin -o \"$<EMAIL>\" \"#L\"", "command": "cd .\\..\\WG02-V194-2\\mdk && fromelf.exe --bin -o \"${KEIL_OUTPUT_DIR}\\${ProjectName}.bin\" \"${KEIL_OUTPUT_DIR}\\${ProjectName}.axf\"", "disable": false, "abortAfterFailed": true}], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}}, "version": "3.6"}