This program is a SMTP_Client example running on Network Dual Stack.
It shows you how to send emails from an embedded application.

Detailed description is available on:
www.keil.com/pack/doc/MW/Network/html/_s_m_t_p__client__example.html

To test this example, connect the evaluation board to a LAN with router.
The example will configure the network parameters automatically.

WARNING!!!

You need to modify SMTP_Client_UIF.c user interface and enter:
 - sender email address,
 - recipient email address,
 - email subject,

and optionally for external SMTP authentication:
 - SMTP username,
 - SMTP password.

Modify also the SMTP_Client.c and enter:
 - SMTP server IP address.

Sent email will have about 40 kBytes.

The SMTP_Client example is available for one target:

STM32F107 Flash:
    Standalone application for MCBSTM32C Board.
    Program code is loaded into on-chip flash.
