This is a FTP_Server example running on Network Dual Stack.
It allows you to manage files on SD Card from PC using FTP client.

Detailed description is available on:
www.keil.com/pack/doc/MW/Network/html/_f_t_p__server__example.html

Use this example to connect an evaluation board to a LAN with router.
You may also connect an evaluation board to PC directly using a direct
or crosslink network cable.
To connect to the FTP server you can use auto-assigned IPv6 address,
IPv4 address assigned by the DHCP server or NBNS resolved local host name.

To test this example, open your FTP client and enter the
address ftp://mcbstm32c/ or ftp://<boards IP address>

IPv4 and IPv6 addresses are printed on display of evaluation board.

Default user    : admin
Default password: <none>

The FTP_Server example is available for one target:

STM32F107 Flash:
    Standalone application for MCBSTM32C Board.
    Program code is loaded into on-chip flash.
