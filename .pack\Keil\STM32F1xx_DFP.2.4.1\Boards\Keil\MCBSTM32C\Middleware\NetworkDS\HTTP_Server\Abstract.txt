This is a HTTP_Server example running on Network Dual Stack.

Detailed description is available on:
www.keil.com/pack/doc/MW/Network/html/_compact__web__server__example.html

Use this example to connect an evaluation board to a LAN with router.
You may also connect an evaluation board to PC directly using a direct
or crosslink network cable.
To connect to the HTTP server you can use auto-assigned IPv6 address,
IPv4 address assigned by the DHCP server or NBNS resolved local host name.

To test this example, open your web browser and enter the
address http://mcbstm32c/ or http://<boards IP address>

IPv4 and IPv6 addresses are printed on display of evaluation board.

Default user    : admin
Default password: <none>

The HTTP_Server example is available for one target:

STM32F107 Flash:
    Standalone application for MCBSTM32C Board.
    Program code is loaded into on-chip flash.
