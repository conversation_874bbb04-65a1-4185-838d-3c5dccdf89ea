/*------------------------------------------------------------------------------
 * MDK Middleware - Component ::Network:Service
 * Copyright (c) 2004-2015 ARM Germany GmbH. All rights reserved.
 *------------------------------------------------------------------------------
 * Name:    Net_Config_FTP_Server.h
 * Purpose: Network Configuration for FTP Server
 * Rev.:    V7.0.0
 *----------------------------------------------------------------------------*/

//-------- <<< Use Configuration Wizard in Context Menu >>> --------------------

// <h>FTP Server
#define FTP_SERVER_ENABLE       1

//   <o>Number of Sessions <1-10>
//   <i>Number of simultaneously active FTP Sessions
//   <i>Default: 1
#define FTP_SERVER_NUM_SESSIONS 3

//   <o>Port Number <1-65535>
//   <i>Listening port number
//   <i>Default: 21
#define FTP_SERVER_PORT_NUM     21

//   <s.50>Welcome Message
//   <i>Optional message, which overrides the default welcome message.
//   <i>Default: ""
#define FTP_SERVER_MESSAGE      ""

//   <o>Idle Session Timeout in seconds <0-3600>
//   <i>When timeout expires, the connection is closed.
//   <i>A value of 0 disables disconnection on timeout.
//   <i>Default: 120
#define FTP_SERVER_TOUT         120

//   <e>Enable User Authentication
//   <i>When enabled, requires authentication of the user through
//   <i>the credentials to access the server.
#define FTP_SERVER_AUTH_ENABLE  1

//     <e>Built-in Administrator Account
//     <i>Enable the built-in Administrator account on the server
//     <i>Default: Enabled
#define FTP_SERVER_AUTH_ADMIN   1

//       <s.15>Administrator Username
//       <i>Default: "admin"
#define FTP_SERVER_AUTH_USER    "admin"

//       <s.15>Administrator Password
//       <i>Default: ""
#define FTP_SERVER_AUTH_PASS    ""
//     </e>
//   </e>

//   <e>Enable Root Folder
//   <i>When enabled, the system prepends the root folder to the file name
//   <i>when opening files on local drive.
//   <i>Default: Disabled
#define FTP_SERVER_ROOT_ENABLE  0

//     <s.80>Local Root Folder
//     <i>Absolute path to the local root folder
//     <i>*** Use unix style '/' folder separators ***
//     <i>Default: "/FTP_root"
#define FTP_SERVER_ROOT_FOLDER  "/FTP_root"
//   </e>

// </h>
