/*-----------------------------------------------------------------------------
 * Name:    GLCD_Fonts.c
 * Purpose: Graphic fonts 6x8 (WxH) and 16x24 with horizontal pixel packing
 * Rev.:    1.00
 *----------------------------------------------------------------------------*/

/* Copyright (c) 2013 - 2014 ARM LIMITED

   All rights reserved.
   Redistribution and use in source and binary forms, with or without
   modification, are permitted provided that the following conditions are met:
   - Redistributions of source code must retain the above copyright
     notice, this list of conditions and the following disclaimer.
   - Redistributions in binary form must reproduce the above copyright
     notice, this list of conditions and the following disclaimer in the
     documentation and/or other materials provided with the distribution.
   - Neither the name of <PERSON>M nor the names of its contributors may be used
     to endorse or promote products derived from this software without
     specific prior written permission.
   *
   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
   AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
   IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
   ARE DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDERS AND CONTRIBUTORS BE
   LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
   CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
   SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
   INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
   CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
   ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
   POSSIBILITY OF SUCH DAMAGE.
   ---------------------------------------------------------------------------*/

#include "Board_GLCD.h"

static const uint8_t Font_6x8_h[(144-32)*8] = {
  /* 0x20: Space ' ' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x21: '!' */
  0x04, 0x04, 0x04, 0x04, 0x04, 0x00, 0x04, 0x00,
  /* 0x22: '"' */
  0x0A, 0x0A, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x23: '#' */
  0x0A, 0x0A, 0x1F, 0x0A, 0x1F, 0x0A, 0x0A, 0x00,
  /* 0x24: '$' */
  0x04, 0x1E, 0x05, 0x0E, 0x14, 0x0F, 0x04, 0x00,
  /* 0x25: '%' */
  0x03, 0x13, 0x08, 0x04, 0x02, 0x19, 0x18, 0x00,
  /* 0x26: '&' */
  0x02, 0x05, 0x05, 0x02, 0x15, 0x09, 0x16, 0x00,
  /* 0x27: ''' */
  0x0C, 0x0C, 0x04, 0x02, 0x00, 0x00, 0x00, 0x00,
  /* 0x28: '(' */
  0x08, 0x04, 0x02, 0x02, 0x02, 0x04, 0x08, 0x00,
  /* 0x29: ')' */
  0x02, 0x04, 0x08, 0x08, 0x08, 0x04, 0x02, 0x00,
  /* 0x2A: '*' */
  0x00, 0x04, 0x15, 0x0E, 0x0E, 0x15, 0x04, 0x00,
  /* 0x2B: '+' */
  0x00, 0x04, 0x04, 0x1F, 0x04, 0x04, 0x00, 0x00,
  /* 0x2C: ',' */
  0x00, 0x00, 0x00, 0x00, 0x0C, 0x0C, 0x04, 0x02,
  /* 0x2D: '-' */
  0x00, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x00,
  /* 0x2E: '.' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x0C, 0x00,
  /* 0x2F: '/' */
  0x00, 0x10, 0x08, 0x04, 0x02, 0x01, 0x00, 0x00,
  /* 0x30: '0' */
  0x0E, 0x11, 0x13, 0x15, 0x19, 0x11, 0x0E, 0x00,
  /* 0x31: '1' */
  0x04, 0x06, 0x04, 0x04, 0x04, 0x04, 0x0E, 0x00,
  /* 0x32: '2' */
  0x0E, 0x11, 0x10, 0x0E, 0x01, 0x01, 0x1F, 0x00,
  /* 0x33: '3' */
  0x1F, 0x10, 0x08, 0x0C, 0x10, 0x11, 0x0E, 0x00,
  /* 0x34: '4' */
  0x08, 0x0C, 0x0A, 0x09, 0x1F, 0x08, 0x08, 0x00,
  /* 0x35: '5' */
  0x1F, 0x01, 0x0F, 0x10, 0x10, 0x11, 0x0E, 0x00,
  /* 0x36: '6' */
  0x1C, 0x02, 0x01, 0x0F, 0x11, 0x11, 0x0E, 0x00,
  /* 0x37: '7' */
  0x1F, 0x10, 0x10, 0x08, 0x04, 0x02, 0x01, 0x00,
  /* 0x38: '8' */
  0x0E, 0x11, 0x11, 0x0E, 0x11, 0x11, 0x0E, 0x00,
  /* 0x39: '9' */
  0x0E, 0x11, 0x11, 0x1E, 0x10, 0x08, 0x07, 0x00,
  /* 0x3A: ':' */
  0x00, 0x00, 0x04, 0x00, 0x04, 0x00, 0x00, 0x00,
  /* 0x3B: ';' */
  0x00, 0x00, 0x04, 0x00, 0x04, 0x04, 0x02, 0x00,
  /* 0x3C: '<' */
  0x10, 0x08, 0x04, 0x02, 0x04, 0x08, 0x10, 0x00,
  /* 0x3D: '=' */
  0x00, 0x00, 0x1F, 0x00, 0x1F, 0x00, 0x00, 0x00,
  /* 0x3E: '>' */
  0x02, 0x04, 0x08, 0x10, 0x08, 0x04, 0x02, 0x00,
  /* 0x3F: '?' */
  0x0E, 0x11, 0x10, 0x0C, 0x04, 0x00, 0x04, 0x00,
  /* 0x40: '@' */
  0x0E, 0x11, 0x15, 0x1D, 0x0D, 0x01, 0x1E, 0x00,
  /* 0x41: 'A' */
  0x04, 0x0A, 0x11, 0x11, 0x1F, 0x11, 0x11, 0x00,
  /* 0x42: 'B' */
  0x0F, 0x11, 0x11, 0x0F, 0x11, 0x11, 0x0F, 0x00,
  /* 0x43: 'C' */
  0x0E, 0x11, 0x01, 0x01, 0x01, 0x11, 0x0E, 0x00,
  /* 0x44: 'D' */
  0x0F, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0F, 0x00,
  /* 0x45: 'E' */
  0x1F, 0x01, 0x01, 0x0F, 0x01, 0x01, 0x1F, 0x00,
  /* 0x46: 'F' */
  0x1F, 0x01, 0x01, 0x0F, 0x01, 0x01, 0x01, 0x00,
  /* 0x47: 'G' */
  0x1E, 0x11, 0x01, 0x01, 0x19, 0x11, 0x1E, 0x00,
  /* 0x48: 'H' */
  0x11, 0x11, 0x11, 0x1F, 0x11, 0x11, 0x11, 0x00,
  /* 0x49: 'I' */
  0x0E, 0x04, 0x04, 0x04, 0x04, 0x04, 0x0E, 0x00,
  /* 0x4A: 'J' */
  0x1C, 0x08, 0x08, 0x08, 0x08, 0x09, 0x06, 0x00,
  /* 0x4B: 'K' */
  0x11, 0x09, 0x05, 0x03, 0x05, 0x09, 0x11, 0x00,
  /* 0x4C: 'L' */
  0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x1F, 0x00,
  /* 0x4D: 'M' */
  0x11, 0x1B, 0x15, 0x15, 0x15, 0x11, 0x11, 0x00,
  /* 0x4E: 'N' */
  0x11, 0x11, 0x13, 0x15, 0x19, 0x11, 0x11, 0x00,
  /* 0x4F: 'O' */
  0x0E, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0E, 0x00,
  /* 0x50: 'P' */
  0x0F, 0x11, 0x11, 0x0F, 0x01, 0x01, 0x01, 0x00,
  /* 0x51: 'Q' */
  0x0E, 0x11, 0x11, 0x11, 0x15, 0x09, 0x16, 0x00,
  /* 0x52: 'R' */
  0x0F, 0x11, 0x11, 0x0F, 0x05, 0x09, 0x11, 0x00,
  /* 0x53: 'S' */
  0x0E, 0x11, 0x01, 0x0E, 0x10, 0x11, 0x0E, 0x00,
  /* 0x54: 'T' */
  0x1F, 0x15, 0x04, 0x04, 0x04, 0x04, 0x04, 0x00,
  /* 0x55: 'U' */
  0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0E, 0x00,
  /* 0x56: 'V' */
  0x11, 0x11, 0x11, 0x11, 0x11, 0x0A, 0x04, 0x00,
  /* 0x57: 'W' */
  0x11, 0x11, 0x11, 0x15, 0x15, 0x15, 0x0A, 0x00,
  /* 0x58: 'X' */
  0x11, 0x11, 0x0A, 0x04, 0x0A, 0x11, 0x11, 0x00,
  /* 0x59: 'Y' */
  0x11, 0x11, 0x0A, 0x04, 0x04, 0x04, 0x04, 0x00,
  /* 0x5A: 'Z' */
  0x1F, 0x10, 0x08, 0x0E, 0x02, 0x01, 0x1F, 0x00,
  /* 0x5B: '[' */
  0x1E, 0x02, 0x02, 0x02, 0x02, 0x02, 0x1E, 0x00,
  /* 0x5C: '\' */
  0x00, 0x01, 0x02, 0x04, 0x08, 0x10, 0x00, 0x00,
  /* 0x5D: ']' */
  0x1E, 0x10, 0x10, 0x10, 0x10, 0x10, 0x1E, 0x00,
  /* 0x5E: '^' */
  0x04, 0x0A, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x5F: '_' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0x00,
  /* 0x60: ''' */
  0x06, 0x06, 0x04, 0x08, 0x00, 0x00, 0x00, 0x00,
  /* 0x61: 'a' */
  0x00, 0x00, 0x06, 0x08, 0x0E, 0x09, 0x1E, 0x00,
  /* 0x62: 'b' */
  0x01, 0x01, 0x0D, 0x13, 0x11, 0x13, 0x0D, 0x00,
  /* 0x63: 'c' */
  0x00, 0x00, 0x0E, 0x11, 0x01, 0x11, 0x0E, 0x00,
  /* 0x64: 'd' */
  0x10, 0x10, 0x16, 0x19, 0x11, 0x19, 0x16, 0x00,
  /* 0x65: 'e' */
  0x00, 0x00, 0x0E, 0x11, 0x1F, 0x01, 0x0E, 0x00,
  /* 0x66: 'f' */
  0x08, 0x14, 0x04, 0x0E, 0x04, 0x04, 0x04, 0x00,
  /* 0x67: 'g' */
  0x00, 0x00, 0x0E, 0x19, 0x19, 0x16, 0x10, 0x0E,
  /* 0x68: 'h' */
  0x01, 0x01, 0x0D, 0x13, 0x11, 0x11, 0x11, 0x00,
  /* 0x69: 'i' */
  0x04, 0x00, 0x06, 0x04, 0x04, 0x04, 0x0E, 0x00,
  /* 0x6A: 'j' */
  0x08, 0x00, 0x08, 0x08, 0x08, 0x09, 0x06, 0x00,
  /* 0x6B: 'k' */
  0x01, 0x01, 0x09, 0x05, 0x03, 0x05, 0x09, 0x00,
  /* 0x6C: 'l' */
  0x06, 0x04, 0x04, 0x04, 0x04, 0x04, 0x0E, 0x00,
  /* 0x6D: 'm' */
  0x00, 0x00, 0x0B, 0x15, 0x15, 0x15, 0x15, 0x00,
  /* 0x6E: 'n' */
  0x00, 0x00, 0x0D, 0x13, 0x11, 0x11, 0x11, 0x00,
  /* 0x6F: 'o' */
  0x00, 0x00, 0x0E, 0x11, 0x11, 0x11, 0x0E, 0x00,
  /* 0x70: 'p' */
  0x00, 0x00, 0x0D, 0x13, 0x13, 0x0D, 0x01, 0x01,
  /* 0x71: 'q' */
  0x00, 0x00, 0x16, 0x19, 0x19, 0x16, 0x10, 0x10,
  /* 0x72: 'r' */
  0x00, 0x00, 0x0D, 0x13, 0x01, 0x01, 0x01, 0x00,
  /* 0x73: 's' */
  0x00, 0x00, 0x1E, 0x01, 0x0E, 0x10, 0x0F, 0x00,
  /* 0x74: 't' */
  0x04, 0x04, 0x1F, 0x04, 0x04, 0x14, 0x08, 0x00,
  /* 0x75: 'u' */
  0x00, 0x00, 0x11, 0x11, 0x11, 0x19, 0x16, 0x00,
  /* 0x76: 'v' */
  0x00, 0x00, 0x11, 0x11, 0x11, 0x0A, 0x04, 0x00,
  /* 0x77: 'w' */
  0x00, 0x00, 0x11, 0x11, 0x15, 0x15, 0x0A, 0x00,
  /* 0x78: 'x' */
  0x00, 0x00, 0x11, 0x0A, 0x04, 0x0A, 0x11, 0x00,
  /* 0x79: 'y' */
  0x00, 0x00, 0x11, 0x11, 0x1E, 0x10, 0x11, 0x0E,
  /* 0x7A: 'z' */
  0x00, 0x00, 0x1F, 0x08, 0x04, 0x02, 0x1F, 0x00,
  /* 0x7B: '{' */
  0x08, 0x04, 0x04, 0x02, 0x04, 0x04, 0x08, 0x00,
  /* 0x7C: '|' */
  0x04, 0x04, 0x04, 0x00, 0x04, 0x04, 0x04, 0x00,
  /* 0x7D: '}' */
  0x02, 0x04, 0x04, 0x08, 0x04, 0x04, 0x02, 0x00,
  /* 0x7E: '~' */
  0x02, 0x15, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x7F: ' ' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

  /* Special Symbols  starting at character 0x80 */
  /* 0x80: Circle - Empty */
  0x00, 0x00, 0x0C, 0x12, 0x12, 0x0C, 0x00, 0x00,
  /* 0x81: Circle - Full */
  0x00, 0x00, 0x0C, 0x1E, 0x1E, 0x0C, 0x00, 0x00,
  /* 0x82: Square - Empty */
  0x00, 0x00, 0x1E, 0x12, 0x12, 0x1E, 0x00, 0x00,
  /* 0x83: Square - Full */
  0x00, 0x00, 0x1E, 0x1E, 0x1E, 0x1E, 0x00, 0x00,
  /* 0x84: Up - Empty */
  0x00, 0x00, 0x0C, 0x0C, 0x12, 0x1E, 0x00, 0x00,
  /* 0x85: Up - Full */
  0x00, 0x00, 0x0C, 0x0C, 0x1E, 0x1E, 0x00, 0x00,
  /* 0x86: Down - Empty */
  0x00, 0x00, 0x1E, 0x12, 0x0C, 0x0C, 0x00, 0x00,
  /* 0x87: Down - Full */
  0x00, 0x00, 0x1E, 0x1E, 0x0C, 0x0C, 0x00, 0x00,
  /* 0x88: Left - Empty */
  0x00, 0x00, 0x18, 0x16, 0x16, 0x18, 0x00, 0x00,
  /* 0x89: Left - Full */
  0x00, 0x00, 0x18, 0x1E, 0x1E, 0x18, 0x00, 0x00,
  /* 0x8A: Right - Empty */
  0x00, 0x00, 0x06, 0x1A, 0x1A, 0x06, 0x00, 0x00,
  /* 0x8B: Right - Full */
  0x00, 0x00, 0x06, 0x1E, 0x1E, 0x06, 0x00, 0x00,
  /* 0x8C: Wait - Empty */
  0x00, 0x00, 0x0C, 0x12, 0x12, 0x0C, 0x00, 0x00,
  /* 0x8D: Wait - Full */
  0x00, 0x00, 0x0C, 0x1E, 0x1E, 0x0C, 0x00, 0x00,
  /* 0x8E: Walk - Empty */
  0x00, 0x00, 0x1E, 0x12, 0x12, 0x1E, 0x00, 0x00,
  /* 0x8F: Walk - Full */
  0x00, 0x00, 0x1E, 0x1E, 0x1E, 0x1E, 0x00, 0x00,
};

static const uint8_t Font_16x24_h[(144-32)*48] = {
  /* 0x20: Space ' ' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x21: '!' */
  0x00, 0x00, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00,
  0x80, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x22: '"' */
  0x00, 0x00, 0x00, 0x00, 0xCC, 0x00, 0xCC, 0x00, 0xCC, 0x00, 0xCC, 0x00, 0xCC, 0x00, 0xCC, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x23: '#' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x0C, 0x60, 0x0C,
  0x60, 0x0C, 0x30, 0x06, 0x30, 0x06, 0xFE, 0x1F, 0xFE, 0x1F, 0x30, 0x06, 0x38, 0x07, 0x18, 0x03,
  0xFE, 0x1F, 0xFE, 0x1F, 0x18, 0x03, 0x18, 0x03, 0x8C, 0x01, 0x8C, 0x01, 0x8C, 0x01, 0x00, 0x00,
  /* 0x24: '$' */
  0x00, 0x00, 0x80, 0x00, 0xE0, 0x03, 0xF8, 0x0F, 0x9C, 0x0E, 0x8C, 0x1C, 0x8C, 0x18, 0x8C, 0x00,
  0x98, 0x00, 0xF8, 0x01, 0xE0, 0x07, 0x80, 0x0E, 0x80, 0x1C, 0x8C, 0x18, 0x8C, 0x18, 0x9C, 0x18,
  0xB8, 0x0C, 0xF0, 0x0F, 0xE0, 0x03, 0x80, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x25: '%' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x18, 0x1B, 0x0C, 0x11, 0x0C, 0x11, 0x06, 0x11, 0x06,
  0x11, 0x03, 0x11, 0x03, 0x9B, 0x01, 0x8E, 0x01, 0xC0, 0x38, 0xC0, 0x6C, 0x60, 0x44, 0x60, 0x44,
  0x30, 0x44, 0x30, 0x44, 0x18, 0x44, 0x18, 0x6C, 0x0C, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x26: '&' */
  0x00, 0x00, 0xE0, 0x01, 0xF0, 0x03, 0x38, 0x07, 0x18, 0x06, 0x18, 0x06, 0x30, 0x03, 0xF0, 0x01,
  0xF0, 0x00, 0xF8, 0x00, 0x9C, 0x31, 0x0E, 0x33, 0x06, 0x1E, 0x06, 0x1C, 0x06, 0x1C, 0x06, 0x3F,
  0xFC, 0x73, 0xF0, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x27: ''' */
  0x00, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x28: '(' */
  0x00, 0x00, 0x00, 0x02, 0x00, 0x03, 0x80, 0x01, 0xC0, 0x00, 0xC0, 0x00, 0x60, 0x00, 0x60, 0x00,
  0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00,
  0x60, 0x00, 0x60, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0x80, 0x01, 0x00, 0x03, 0x00, 0x02, 0x00, 0x00,
  /* 0x29: ')' */
  0x00, 0x00, 0x20, 0x00, 0x60, 0x00, 0xC0, 0x00, 0x80, 0x01, 0x80, 0x01, 0x00, 0x03, 0x00, 0x03,
  0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06,
  0x00, 0x03, 0x00, 0x03, 0x80, 0x01, 0x80, 0x01, 0xC0, 0x00, 0x60, 0x00, 0x20, 0x00, 0x00, 0x00,
  /* 0x2A: '*' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0xD8, 0x06, 0xF8, 0x07, 0xE0, 0x01, 0x30, 0x03, 0x38, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x2B: '+' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0xFC, 0x3F, 0xFC, 0x3F, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x2C: ',' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x80, 0x01, 0x80, 0x01, 0x00, 0x01, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x2D: '-' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x07, 0xE0, 0x07, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x2E: '.' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x2F: '/' */
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x03, 0x00, 0x03,
  0x00, 0x03, 0x80, 0x03, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0x60, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x30: '0' */
  0x00, 0x00, 0xE0, 0x03, 0xF0, 0x07, 0x38, 0x0E, 0x18, 0x0C, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x18,
  0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x18, 0x18, 0x0C, 0x38, 0x0E,
  0xF0, 0x07, 0xE0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x31: '1' */
  0x00, 0x00, 0x00, 0x01, 0x80, 0x01, 0xC0, 0x01, 0xF0, 0x01, 0x98, 0x01, 0x88, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x32: '2' */
  0x00, 0x00, 0xE0, 0x03, 0xF8, 0x0F, 0x18, 0x0C, 0x0C, 0x18, 0x0C, 0x18, 0x00, 0x18, 0x00, 0x18,
  0x00, 0x0C, 0x00, 0x06, 0x00, 0x03, 0x80, 0x01, 0xC0, 0x00, 0x60, 0x00, 0x30, 0x00, 0x18, 0x00,
  0xFC, 0x1F, 0xFC, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x33: '3' */
  0x00, 0x00, 0xE0, 0x01, 0xF8, 0x07, 0x18, 0x0E, 0x0C, 0x0C, 0x0C, 0x0C, 0x00, 0x0C, 0x00, 0x06,
  0xC0, 0x03, 0xC0, 0x07, 0x00, 0x0C, 0x00, 0x18, 0x00, 0x18, 0x0C, 0x18, 0x0C, 0x18, 0x18, 0x0C,
  0xF8, 0x07, 0xE0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x34: '4' */
  0x00, 0x00, 0x00, 0x0C, 0x00, 0x0E, 0x00, 0x0F, 0x00, 0x0F, 0x80, 0x0D, 0xC0, 0x0C, 0x60, 0x0C,
  0x60, 0x0C, 0x30, 0x0C, 0x18, 0x0C, 0x0C, 0x0C, 0xFC, 0x3F, 0xFC, 0x3F, 0x00, 0x0C, 0x00, 0x0C,
  0x00, 0x0C, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x35: '5' */
  0x00, 0x00, 0xF8, 0x0F, 0xF8, 0x0F, 0x18, 0x00, 0x18, 0x00, 0x0C, 0x00, 0xEC, 0x03, 0xFC, 0x07,
  0x1C, 0x0E, 0x00, 0x1C, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x0C, 0x18, 0x1C, 0x0C, 0x18, 0x0E,
  0xF8, 0x07, 0xE0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x36: '6' */
  0x00, 0x00, 0xC0, 0x07, 0xF0, 0x0F, 0x38, 0x1C, 0x18, 0x18, 0x18, 0x00, 0x0C, 0x00, 0xCC, 0x03,
  0xEC, 0x0F, 0x3C, 0x0E, 0x1C, 0x1C, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x18, 0x18, 0x1C, 0x38, 0x0E,
  0xF0, 0x07, 0xE0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x37: '7' */
  0x00, 0x00, 0xFC, 0x1F, 0xFC, 0x1F, 0x00, 0x0C, 0x00, 0x06, 0x00, 0x06, 0x00, 0x03, 0x80, 0x03,
  0x80, 0x01, 0xC0, 0x01, 0xC0, 0x00, 0xE0, 0x00, 0x60, 0x00, 0x60, 0x00, 0x70, 0x00, 0x30, 0x00,
  0x30, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x38: '8' */
  0x00, 0x00, 0xE0, 0x03, 0xF0, 0x07, 0x38, 0x0E, 0x18, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x38, 0x06,
  0xF0, 0x07, 0xF0, 0x07, 0x18, 0x0C, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x18, 0x38, 0x0C,
  0xF8, 0x0F, 0xE0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x39: '9' */
  0x00, 0x00, 0xE0, 0x03, 0xF0, 0x07, 0x38, 0x0E, 0x1C, 0x0C, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x18,
  0x1C, 0x1C, 0x38, 0x1E, 0xF8, 0x1B, 0xE0, 0x19, 0x00, 0x18, 0x00, 0x0C, 0x00, 0x0C, 0x1C, 0x0E,
  0xF8, 0x07, 0xF0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x3A: ':' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x80, 0x01,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x80, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x3B: ';' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0x80, 0x01,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x80, 0x01, 0x80, 0x01, 0x00, 0x01, 0x00, 0x01, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x3C: '<' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x10, 0x00, 0x1C, 0x80, 0x0F, 0xE0, 0x03, 0xF8, 0x00, 0x18, 0x00, 0xF8, 0x00, 0xE0, 0x03,
  0x80, 0x0F, 0x00, 0x1C, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x3D: '=' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xF8, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x3E: '>' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x08, 0x00, 0x38, 0x00, 0xF0, 0x01, 0xC0, 0x07, 0x00, 0x1F, 0x00, 0x18, 0x00, 0x1F, 0xC0, 0x07,
  0xF0, 0x01, 0x38, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x3F: '?' */
  0x00, 0x00, 0xE0, 0x03, 0xF8, 0x0F, 0x18, 0x0C, 0x0C, 0x18, 0x0C, 0x18, 0x00, 0x18, 0x00, 0x0C,
  0x00, 0x06, 0x00, 0x03, 0x80, 0x01, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x40: '@' */
  0x00, 0x00, 0x00, 0x00, 0xE0, 0x07, 0x18, 0x18, 0x04, 0x20, 0xC2, 0x29, 0x22, 0x4A, 0x11, 0x44,
  0x09, 0x44, 0x09, 0x44, 0x09, 0x44, 0x09, 0x22, 0x11, 0x13, 0xE2, 0x0C, 0x02, 0x40, 0x04, 0x20,
  0x18, 0x18, 0xE0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x41: 'A' */
  0x00, 0x00, 0x80, 0x03, 0x80, 0x03, 0xC0, 0x06, 0xC0, 0x06, 0xC0, 0x06, 0x60, 0x0C, 0x60, 0x0C,
  0x30, 0x18, 0x30, 0x18, 0x30, 0x18, 0xF8, 0x3F, 0xF8, 0x3F, 0x1C, 0x70, 0x0C, 0x60, 0x0C, 0x60,
  0x06, 0xC0, 0x06, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x42: 'B' */
  0x00, 0x00, 0xFC, 0x03, 0xFC, 0x0F, 0x0C, 0x0C, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x0C,
  0xFC, 0x07, 0xFC, 0x0F, 0x0C, 0x18, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x18,
  0xFC, 0x1F, 0xFC, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x43: 'C' */
  0x00, 0x00, 0xC0, 0x07, 0xF0, 0x1F, 0x38, 0x38, 0x1C, 0x30, 0x0C, 0x70, 0x06, 0x60, 0x06, 0x00,
  0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x60, 0x0C, 0x70, 0x1C, 0x30,
  0xF0, 0x1F, 0xE0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x44: 'D' */
  0x00, 0x00, 0xFE, 0x03, 0xFE, 0x0F, 0x06, 0x0E, 0x06, 0x18, 0x06, 0x18, 0x06, 0x30, 0x06, 0x30,
  0x06, 0x30, 0x06, 0x30, 0x06, 0x30, 0x06, 0x30, 0x06, 0x30, 0x06, 0x18, 0x06, 0x18, 0x06, 0x0E,
  0xFE, 0x0F, 0xFE, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x45: 'E' */
  0x00, 0x00, 0xFC, 0x3F, 0xFC, 0x3F, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00,
  0xFC, 0x1F, 0xFC, 0x1F, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00,
  0xFC, 0x3F, 0xFC, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x46: 'F' */
  0x00, 0x00, 0xF8, 0x3F, 0xF8, 0x3F, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00,
  0xF8, 0x1F, 0xF8, 0x1F, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00,
  0x18, 0x00, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x47: 'G' */
  0x00, 0x00, 0xE0, 0x0F, 0xF8, 0x3F, 0x3C, 0x78, 0x0E, 0x60, 0x06, 0xE0, 0x07, 0xC0, 0x03, 0x00,
  0x03, 0x00, 0x03, 0xFE, 0x03, 0xFE, 0x03, 0xC0, 0x07, 0xC0, 0x06, 0xC0, 0x0E, 0xC0, 0x3C, 0xF0,
  0xF8, 0x3F, 0xE0, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x48: 'H' */
  0x00, 0x00, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30,
  0xFC, 0x3F, 0xFC, 0x3F, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30,
  0x0C, 0x30, 0x0C, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x49: 'I' */
  0x00, 0x00, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x4A: 'J' */
  0x00, 0x00, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06,
  0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x00, 0x06, 0x18, 0x06, 0x18, 0x06, 0x38, 0x07,
  0xF0, 0x03, 0xE0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x4B: 'K' */
  0x00, 0x00, 0x06, 0x30, 0x06, 0x18, 0x06, 0x0C, 0x06, 0x06, 0x06, 0x03, 0x86, 0x01, 0xC6, 0x00,
  0x66, 0x00, 0x76, 0x00, 0xDE, 0x00, 0x8E, 0x01, 0x06, 0x03, 0x06, 0x06, 0x06, 0x0C, 0x06, 0x18,
  0x06, 0x30, 0x06, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x4C: 'L' */
  0x00, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00,
  0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00,
  0xF8, 0x1F, 0xF8, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x4D: 'M' */
  0x00, 0x00, 0x0E, 0xE0, 0x1E, 0xF0, 0x1E, 0xF0, 0x1E, 0xF0, 0x36, 0xD8, 0x36, 0xD8, 0x36, 0xD8,
  0x36, 0xD8, 0x66, 0xCC, 0x66, 0xCC, 0x66, 0xCC, 0xC6, 0xC6, 0xC6, 0xC6, 0xC6, 0xC6, 0xC6, 0xC6,
  0x86, 0xC3, 0x86, 0xC3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x4E: 'N' */
  0x00, 0x00, 0x0C, 0x30, 0x1C, 0x30, 0x3C, 0x30, 0x3C, 0x30, 0x6C, 0x30, 0x6C, 0x30, 0xCC, 0x30,
  0xCC, 0x30, 0x8C, 0x31, 0x0C, 0x33, 0x0C, 0x33, 0x0C, 0x36, 0x0C, 0x36, 0x0C, 0x3C, 0x0C, 0x3C,
  0x0C, 0x38, 0x0C, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x4F: 'O' */
  0x00, 0x00, 0xE0, 0x07, 0xF8, 0x1F, 0x1C, 0x38, 0x0E, 0x70, 0x06, 0x60, 0x03, 0xC0, 0x03, 0xC0,
  0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x06, 0x60, 0x0E, 0x70, 0x1C, 0x38,
  0xF8, 0x1F, 0xE0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x50: 'P' */
  0x00, 0x00, 0xFC, 0x0F, 0xFC, 0x1F, 0x0C, 0x38, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30,
  0x0C, 0x18, 0xFC, 0x1F, 0xFC, 0x07, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00,
  0x0C, 0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x51: 'Q' */
  0x00, 0x00, 0xE0, 0x07, 0xF8, 0x1F, 0x1C, 0x38, 0x0E, 0x70, 0x06, 0x60, 0x03, 0xE0, 0x03, 0xC0,
  0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x07, 0xE0, 0x06, 0x63, 0x0E, 0x3F, 0x1C, 0x3C,
  0xF8, 0x3F, 0xE0, 0xF7, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x52: 'R' */
  0x00, 0x00, 0xFE, 0x0F, 0xFE, 0x1F, 0x06, 0x38, 0x06, 0x30, 0x06, 0x30, 0x06, 0x30, 0x06, 0x38,
  0xFE, 0x1F, 0xFE, 0x07, 0x06, 0x03, 0x06, 0x06, 0x06, 0x0C, 0x06, 0x18, 0x06, 0x18, 0x06, 0x30,
  0x06, 0x30, 0x06, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x53: 'S' */
  0x00, 0x00, 0xE0, 0x03, 0xF8, 0x0F, 0x1C, 0x0C, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x00, 0x1C, 0x00,
  0xF8, 0x03, 0xE0, 0x0F, 0x00, 0x1E, 0x00, 0x38, 0x06, 0x30, 0x06, 0x30, 0x0E, 0x30, 0x1C, 0x1C,
  0xF8, 0x0F, 0xE0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x54: 'T' */
  0x00, 0x00, 0xFE, 0x7F, 0xFE, 0x7F, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x55: 'U' */
  0x00, 0x00, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30,
  0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x0C, 0x30, 0x18, 0x18,
  0xF8, 0x1F, 0xE0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x56: 'V' */
  0x00, 0x00, 0x03, 0x60, 0x06, 0x30, 0x06, 0x30, 0x06, 0x30, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x18,
  0x18, 0x0C, 0x18, 0x0C, 0x38, 0x0E, 0x30, 0x06, 0x30, 0x06, 0x70, 0x07, 0x60, 0x03, 0x60, 0x03,
  0xC0, 0x01, 0xC0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x57: 'W' */
  0x00, 0x00, 0x03, 0x60, 0xC3, 0x61, 0xC3, 0x61, 0xC3, 0x61, 0x66, 0x33, 0x66, 0x33, 0x66, 0x33,
  0x66, 0x33, 0x66, 0x33, 0x66, 0x33, 0x6C, 0x1B, 0x6C, 0x1B, 0x6C, 0x1B, 0x2C, 0x1A, 0x3C, 0x1E,
  0x38, 0x0E, 0x38, 0x0E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x58: 'X' */
  0x00, 0x00, 0x0F, 0xE0, 0x0C, 0x70, 0x18, 0x30, 0x30, 0x18, 0x70, 0x0C, 0x60, 0x0E, 0xC0, 0x07,
  0x80, 0x03, 0x80, 0x03, 0xC0, 0x03, 0xE0, 0x06, 0x70, 0x0C, 0x30, 0x1C, 0x18, 0x18, 0x0C, 0x30,
  0x0E, 0x60, 0x07, 0xE0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x59: 'Y' */
  0x00, 0x00, 0x03, 0xC0, 0x06, 0x60, 0x0C, 0x30, 0x1C, 0x38, 0x38, 0x18, 0x30, 0x0C, 0x60, 0x06,
  0xE0, 0x07, 0xC0, 0x03, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x5A: 'Z' */
  0x00, 0x00, 0xFC, 0x7F, 0xFC, 0x7F, 0x00, 0x60, 0x00, 0x30, 0x00, 0x18, 0x00, 0x0C, 0x00, 0x06,
  0x00, 0x03, 0x80, 0x01, 0xC0, 0x00, 0x60, 0x00, 0x30, 0x00, 0x18, 0x00, 0x0C, 0x00, 0x06, 0x00,
  0xFE, 0x7F, 0xFE, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x5B: '[' */
  0x00, 0x00, 0xE0, 0x03, 0xE0, 0x03, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
  0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00,
  0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0xE0, 0x03, 0xE0, 0x03, 0x00, 0x00,
  /* 0x5C: '\' */
  0x00, 0x00, 0x30, 0x00, 0x30, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0xC0, 0x00, 0xC0, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03,
  0x00, 0x06, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x5D: ']' */
  0x00, 0x00, 0xE0, 0x03, 0xE0, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03,
  0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03,
  0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0x00, 0x03, 0xE0, 0x03, 0xE0, 0x03, 0x00, 0x00,
  /* 0x5E: '^' */
  0x00, 0x00, 0x00, 0x00, 0xC0, 0x01, 0xC0, 0x01, 0x60, 0x03, 0x60, 0x03, 0x60, 0x03, 0x30, 0x06,
  0x30, 0x06, 0x18, 0x0C, 0x18, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x5F: '_' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x60: ''' */
  0x00, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x61: 'a' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF0, 0x03, 0xF8, 0x07,
  0x1C, 0x0C, 0x0C, 0x0C, 0x00, 0x0F, 0xF0, 0x0F, 0xF8, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x1C, 0x0F,
  0xF8, 0x0F, 0xF0, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x62: 'b' */
  0x00, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0xD8, 0x03, 0xF8, 0x0F,
  0x38, 0x0C, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x38, 0x0C,
  0xF8, 0x0F, 0xD8, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x63: 'c' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x03, 0xF0, 0x07,
  0x30, 0x0E, 0x18, 0x0C, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x0C, 0x30, 0x0E,
  0xF0, 0x07, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x64: 'd' */
  0x00, 0x00, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0xC0, 0x1B, 0xF0, 0x1F,
  0x30, 0x1C, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x30, 0x1C,
  0xF0, 0x1F, 0xC0, 0x1B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x65: 'e' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x03, 0xF0, 0x0F,
  0x30, 0x0C, 0x18, 0x18, 0xF8, 0x1F, 0xF8, 0x1F, 0x18, 0x00, 0x18, 0x00, 0x38, 0x18, 0x30, 0x1C,
  0xF0, 0x0F, 0xC0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x66: 'f' */
  0x00, 0x00, 0x80, 0x0F, 0xC0, 0x0F, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xF0, 0x07, 0xF0, 0x07,
  0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x67: 'g' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x0D, 0xF8, 0x0F,
  0x18, 0x0E, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x18, 0x0E,
  0xF8, 0x0F, 0xE0, 0x0D, 0x00, 0x0C, 0x0C, 0x0C, 0x1C, 0x06, 0xF8, 0x07, 0xF0, 0x01, 0x00, 0x00,
  /* 0x68: 'h' */
  0x00, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0xD8, 0x07, 0xF8, 0x0F,
  0x38, 0x1C, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18,
  0x18, 0x18, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x69: 'i' */
  0x00, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x6A: 'j' */
  0x00, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xF8, 0x00, 0x78, 0x00, 0x00, 0x00,
  /* 0x6B: 'k' */
  0x00, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x00, 0x0C, 0x0C, 0x0C, 0x06,
  0x0C, 0x03, 0x8C, 0x01, 0xCC, 0x00, 0x6C, 0x00, 0xFC, 0x00, 0x9C, 0x01, 0x8C, 0x03, 0x0C, 0x03,
  0x0C, 0x06, 0x0C, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x6C: 'l' */
  0x00, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0xC0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x6D: 'm' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7C, 0x3C, 0xFF, 0x7E,
  0xC7, 0xE3, 0x83, 0xC1, 0x83, 0xC1, 0x83, 0xC1, 0x83, 0xC1, 0x83, 0xC1, 0x83, 0xC1, 0x83, 0xC1,
  0x83, 0xC1, 0x83, 0xC1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x6E: 'n' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x98, 0x07, 0xF8, 0x0F,
  0x38, 0x1C, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18,
  0x18, 0x18, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x6F: 'o' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x03, 0xF0, 0x0F,
  0x30, 0x0C, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x30, 0x0C,
  0xF0, 0x0F, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x70: 'p' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xD8, 0x03, 0xF8, 0x0F,
  0x38, 0x0C, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x38, 0x0C,
  0xF8, 0x0F, 0xD8, 0x03, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x00, 0x00,
  /* 0x71: 'q' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x1B, 0xF0, 0x1F,
  0x30, 0x1C, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x30, 0x1C,
  0xF0, 0x1F, 0xC0, 0x1B, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x18, 0x00, 0x00,
  /* 0x72: 'r' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xB0, 0x07, 0xF0, 0x03,
  0x70, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00,
  0x30, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x73: 's' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x03, 0xF0, 0x03,
  0x38, 0x0E, 0x18, 0x0C, 0x38, 0x00, 0xF0, 0x03, 0xC0, 0x07, 0x00, 0x0C, 0x18, 0x0C, 0x38, 0x0E,
  0xF0, 0x07, 0xE0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x74: 't' */
  0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xF0, 0x07, 0xF0, 0x07,
  0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0xC0, 0x07, 0x80, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x75: 'u' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x18, 0x18,
  0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x38, 0x1C,
  0xF0, 0x1F, 0xE0, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x76: 'v' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x18, 0x18, 0x0C,
  0x18, 0x0C, 0x18, 0x0C, 0x30, 0x06, 0x30, 0x06, 0x30, 0x06, 0x60, 0x03, 0x60, 0x03, 0x60, 0x03,
  0xC0, 0x01, 0xC0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x77: 'w' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC1, 0x41, 0xC1, 0x41,
  0xC3, 0x61, 0x63, 0x63, 0x63, 0x63, 0x63, 0x63, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x1C, 0x1C,
  0x1C, 0x1C, 0x1C, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x78: 'x' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1C, 0x38, 0x38, 0x1C,
  0x30, 0x0C, 0x60, 0x06, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03, 0xC0, 0x03, 0x60, 0x06, 0x30, 0x0C,
  0x38, 0x1C, 0x1C, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x79: 'y' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x30, 0x30, 0x18,
  0x30, 0x18, 0x70, 0x18, 0x60, 0x0C, 0x60, 0x0C, 0xE0, 0x0C, 0xC0, 0x06, 0xC0, 0x06, 0x80, 0x03,
  0x80, 0x03, 0x80, 0x03, 0x80, 0x01, 0x80, 0x01, 0xC0, 0x01, 0xF0, 0x00, 0x70, 0x00, 0x00, 0x00,
  /* 0x7A: 'z' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x1F, 0xFC, 0x1F,
  0x00, 0x0C, 0x00, 0x06, 0x00, 0x03, 0x80, 0x01, 0xC0, 0x00, 0x60, 0x00, 0x30, 0x00, 0x18, 0x00,
  0xFC, 0x1F, 0xFC, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x7B: '{' */
  0x00, 0x00, 0x00, 0x03, 0x80, 0x01, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0xC0, 0x00, 0x60, 0x00, 0x60, 0x00, 0x30, 0x00, 0x60, 0x00, 0x40, 0x00, 0xC0, 0x00, 0xC0, 0x00,
  0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0xC0, 0x00, 0x80, 0x01, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00,
  /* 0x7C: '|' */
  0x00, 0x00, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x00, 0x00,
  /* 0x7D: '}' */
  0x00, 0x00, 0x60, 0x00, 0xC0, 0x00, 0xC0, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x00, 0x03, 0x00, 0x03, 0x00, 0x06, 0x00, 0x03, 0x00, 0x01, 0x80, 0x01, 0x80, 0x01,
  0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0xC0, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x7E: '~' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xF0, 0x10, 0xF8, 0x1F, 0x08, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x7F: ' ' */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

  /* Special Symbols  starting at character 0x80 */
  /* 0x80: Circle - Empty */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x03, 0x30, 0x0C, 0x08, 0x10,
  0x04, 0x20, 0x04, 0x20, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x02, 0x40, 0x04, 0x20,
  0x04, 0x20, 0x08, 0x10, 0x30, 0x0C, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x81: Circle - Full */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x03, 0xF0, 0x0F, 0xF8, 0x1F,
  0xFC, 0x3F, 0xFC, 0x3F, 0xFE, 0x7F, 0xFE, 0x7F, 0xFE, 0x7F, 0xFE, 0x7F, 0xFE, 0x7F, 0xFC, 0x3F,
  0xFC, 0x3F, 0xF8, 0x1F, 0xF0, 0x0F, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x82: Square - Empty */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x07,
  0xF0, 0x0F, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0xF0, 0x0F,
  0xE0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x83: Square - Full */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x07,
  0xF0, 0x0F, 0xF8, 0x1F, 0xF8, 0x1F, 0xF8, 0x1F, 0xF8, 0x1F, 0xF8, 0x1F, 0xF8, 0x1F, 0xF0, 0x0F,
  0xE0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x84: Up - Empty */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0xC0, 0x03, 0x60, 0x06, 0x30, 0x0C,
  0x18, 0x18, 0x18, 0x18, 0xF8, 0x1F, 0xF8, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x85: Up - Full */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0xC0, 0x03, 0xE0, 0x07, 0xF0, 0x0F,
  0xF8, 0x1F, 0xF8, 0x1F, 0xF8, 0x1F, 0xF8, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x86: Down - Empty */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0x1F, 0xF8, 0x1F, 0x18, 0x18, 0x18, 0x18,
  0x30, 0x0C, 0x60, 0x06, 0xC0, 0x03, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x87: Down - Full */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xF8, 0x1F, 0xF8, 0x1F, 0xF8, 0x1F, 0xF8, 0x1F,
  0xF0, 0x0F, 0xE0, 0x07, 0xC0, 0x03, 0x80, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x88: Left - Empty */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x01,
  0xF0, 0x01, 0x98, 0x01, 0x8C, 0x01, 0x86, 0x01, 0x86, 0x01, 0x8C, 0x01, 0x98, 0x01, 0xF0, 0x01,
  0xE0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x89: Left - Full */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xE0, 0x01,
  0xF0, 0x01, 0xF8, 0x01, 0xFC, 0x01, 0xFE, 0x01, 0xFE, 0x01, 0xFC, 0x01, 0xF8, 0x01, 0xF0, 0x01,
  0xE0, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x8A: Right - Empty */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x07,
  0x80, 0x0F, 0x80, 0x19, 0x80, 0x31, 0x80, 0x61, 0x80, 0x61, 0x80, 0x31, 0x80, 0x19, 0x80, 0x0F,
  0x80, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x8B: Right - Full */
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x07,
  0x80, 0x0F, 0x80, 0x1F, 0x80, 0x3F, 0x80, 0x7F, 0x80, 0x7F, 0x80, 0x3F, 0x80, 0x1F, 0x80, 0x0F,
  0x80, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  /* 0x8C: Wait - Empty */
  0x00, 0x00, 0xC0, 0x01, 0x20, 0x02, 0x20, 0x02, 0x40, 0x01, 0x30, 0x06, 0x08, 0x08, 0x08, 0x08,
  0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x20, 0x02, 0x20, 0x02, 0x20, 0x02,
  0x20, 0x02, 0x20, 0x02, 0x20, 0x02, 0x20, 0x02, 0x20, 0x02, 0x20, 0x02, 0x20, 0x02, 0x00, 0x00,
  /* 0x8D: Wait - Full */
  0x00, 0x00, 0xC0, 0x01, 0xE0, 0x03, 0xE0, 0x03, 0xC0, 0x01, 0xF0, 0x07, 0xD8, 0x0D, 0xD8, 0x0D,
  0xD8, 0x0D, 0xD8, 0x0D, 0xD8, 0x0D, 0xD8, 0x0D, 0xD8, 0x0D, 0x60, 0x03, 0x60, 0x03, 0x60, 0x03,
  0x60, 0x03, 0x60, 0x03, 0x60, 0x03, 0x60, 0x03, 0x60, 0x03, 0x60, 0x03, 0x60, 0x03, 0x00, 0x00,
  /* 0x8E: Walk - Empty */
  0x00, 0x00, 0xC0, 0x01, 0x20, 0x02, 0x20, 0x02, 0x40, 0x01, 0x30, 0x06, 0x08, 0x08, 0x08, 0x08,
  0x08, 0x08, 0x04, 0x10, 0x02, 0x20, 0x02, 0x20, 0x40, 0x01, 0x20, 0x02, 0x20, 0x02, 0x10, 0x04,
  0x08, 0x08, 0x08, 0x08, 0x04, 0x10, 0x04, 0x10, 0x04, 0x20, 0x04, 0x40, 0x00, 0x00, 0x00, 0x00,
  /* 0x8F: Walk - Full */
  0x00, 0x00, 0xC0, 0x01, 0xE0, 0x03, 0xE0, 0x03, 0xC0, 0x01, 0xF0, 0x07, 0xD8, 0x0D, 0xD8, 0x0D,
  0xD8, 0x0D, 0xCC, 0x19, 0xC6, 0x31, 0xC2, 0x61, 0xC0, 0x01, 0x60, 0x03, 0x60, 0x03, 0x70, 0x06,
  0x38, 0x0C, 0x18, 0x0C, 0x0C, 0x18, 0x0C, 0x18, 0x0C, 0x30, 0x0C, 0x60, 0x00, 0x00, 0x00, 0x00,
};

GLCD_FONT GLCD_Font_6x8 = {
  6,                                    ///< Character width
  8,                                    ///< Character height
  32,                                   ///< Character offset
  112,                                  ///< Character count
  Font_6x8_h                            ///< Characters bitmaps
};

GLCD_FONT GLCD_Font_16x24 = {
  16,                                   ///< Character width
  24,                                   ///< Character height
  32,                                   ///< Character offset
  112,                                  ///< Character count
  Font_16x24_h                          ///< Characters bitmaps
};
